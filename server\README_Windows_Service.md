# Socks Proxy Server - Windows服务

本项目已经成功封装为Windows服务，使用`golang.org/x/sys/windows/svc`库实现。

## 功能特性

- ✅ 支持Windows服务安装/卸载
- ✅ 支持服务启动/停止
- ✅ 支持调试模式运行
- ✅ 支持普通控制台模式运行
- ✅ 自动检测运行环境（服务模式 vs 交互模式）
- ✅ 完整的Windows事件日志支持
- ✅ 优雅关闭机制

## 使用方法

### 1. 编译项目

```bash
go build -o server.exe
```

### 2. 服务管理命令

#### 安装服务
```bash
# 以管理员身份运行
server.exe -install
```

#### 卸载服务
```bash
# 以管理员身份运行
server.exe -uninstall
```

#### 启动服务
```bash
# 以管理员身份运行
server.exe -start
```

#### 停止服务
```bash
# 以管理员身份运行
server.exe -stop
```

### 3. 运行模式

#### 服务模式运行
```bash
# 手动以服务模式运行（用于调试）
server.exe -service
```

#### 调试模式运行
```bash
# 以调试模式运行服务（输出到控制台）
server.exe -debug
```

#### 普通控制台模式
```bash
# 直接运行（开发调试用）
server.exe
```

### 4. 使用服务管理器（推荐）

我们提供了一个方便的批处理文件 `service_manager.bat`：

```bash
# 以管理员身份运行
service_manager.bat
```

该脚本提供了图形化菜单，包含以下功能：
- 安装/卸载服务
- 启动/停止服务
- 查看服务状态
- 查看服务日志
- 编译项目
- 调试模式运行

## 服务配置

- **服务名称**: `SocksProxyServer`
- **显示名称**: `Socks Proxy Server`
- **描述**: `A proxy server for tunneling connections`
- **启动类型**: 自动启动
- **运行账户**: 本地系统账户

## 日志查看

### Windows事件日志
服务运行时的日志会记录到Windows事件日志中：
1. 打开"事件查看器"
2. 导航到"Windows日志" > "应用程序"
3. 查找来源为"SocksProxyServer"的事件

### 命令行查看日志
```bash
# 查看最近20条应用程序日志
wevtutil qe Application /c:20 /f:text /q:"*[System[Provider[@Name='SocksProxyServer']]]"
```

## 文件结构

```
server/
├── main.go                    # 主入口文件，处理命令行参数和运行模式
├── service/
│   ├── windows_service.go     # Windows服务实现
│   └── service_main.go        # 服务主逻辑
├── service_manager.bat        # 服务管理批处理脚本
└── README_Windows_Service.md  # 本说明文件
```

## 注意事项

1. **管理员权限**: 安装、卸载、启动、停止服务都需要管理员权限
2. **防火墙**: 确保防火墙允许服务监听的端口
3. **配置文件**: 服务会使用默认配置，也可以通过 `-config` 参数指定配置文件路径
4. **Windows兼容性**: 此功能仅在Windows系统上可用

## 故障排除

### 服务安装失败
- 确保以管理员身份运行
- 检查服务是否已经存在
- 查看事件日志获取详细错误信息

### 服务启动失败
- 检查端口是否被占用
- 查看事件日志获取详细错误信息
- 确保配置文件路径正确

### 服务无法停止
- 使用服务管理器强制停止
- 检查是否有活跃的连接阻止服务关闭

## 开发说明

### 添加新功能
如需添加新功能到服务中，请修改 `service/service_main.go` 文件中的 `StartMainService` 函数。

### 修改服务配置
服务的基本配置（名称、描述等）在 `service/windows_service.go` 文件的常量部分定义。

### 扩展命令行参数
新的命令行参数应该在 `main.go` 文件中添加，并在 `service/service_main.go` 中处理。
