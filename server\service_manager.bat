@echo off
setlocal enabledelayedexpansion

:: 检查管理员权限
net session >nul 2>&1
if %errorLevel% neq 0 (
    echo 此脚本需要管理员权限运行
    echo 请右键点击并选择"以管理员身份运行"
    pause
    exit /b 1
)

:: 设置变量
set SERVICE_NAME=SocksProxyServer
set EXE_NAME=server.exe

:: 检查可执行文件是否存在
if not exist "%EXE_NAME%" (
    echo 错误: %EXE_NAME% 不存在
    echo 请先编译项目: go build -o %EXE_NAME%
    pause
    exit /b 1
)

:menu
cls
echo ========================================
echo     Socks Proxy Server 服务管理器
echo ========================================
echo.
echo 1. 安装服务
echo 2. 卸载服务
echo 3. 启动服务
echo 4. 停止服务
echo 5. 查看服务状态
echo 6. 查看服务日志
echo 7. 编译项目
echo 8. 调试模式运行
echo 9. 退出
echo.
set /p choice=请选择操作 (1-9): 

if "%choice%"=="1" goto install
if "%choice%"=="2" goto uninstall
if "%choice%"=="3" goto start
if "%choice%"=="4" goto stop
if "%choice%"=="5" goto status
if "%choice%"=="6" goto logs
if "%choice%"=="7" goto build
if "%choice%"=="8" goto debug
if "%choice%"=="9" goto exit
goto menu

:install
echo 正在安装服务...
%EXE_NAME% -install
if %errorLevel% equ 0 (
    echo 服务安装成功！
) else (
    echo 服务安装失败！
)
pause
goto menu

:uninstall
echo 正在卸载服务...
%EXE_NAME% -uninstall
if %errorLevel% equ 0 (
    echo 服务卸载成功！
) else (
    echo 服务卸载失败！
)
pause
goto menu

:start
echo 正在启动服务...
%EXE_NAME% -start
if %errorLevel% equ 0 (
    echo 服务启动成功！
) else (
    echo 服务启动失败！
)
pause
goto menu

:stop
echo 正在停止服务...
%EXE_NAME% -stop
if %errorLevel% equ 0 (
    echo 服务停止成功！
) else (
    echo 服务停止失败！
)
pause
goto menu

:status
echo 查看服务状态...
sc query %SERVICE_NAME%
pause
goto menu

:logs
echo 查看服务日志...
echo 请在事件查看器中查看 Windows 日志 ^> 应用程序
echo 或者使用以下命令查看：
echo wevtutil qe Application /c:20 /f:text /q:"*[System[Provider[@Name='%SERVICE_NAME%']]]"
echo.
wevtutil qe Application /c:20 /f:text /q:"*[System[Provider[@Name='%SERVICE_NAME%']]]"
pause
goto menu

:build
echo 正在编译项目...
go build -o %EXE_NAME%
if %errorLevel% equ 0 (
    echo 编译成功！
) else (
    echo 编译失败！
)
pause
goto menu

:debug
echo 正在以调试模式运行...
echo 按 Ctrl+C 停止服务
%EXE_NAME%
pause
goto menu

:exit
echo 退出服务管理器
exit /b 0
